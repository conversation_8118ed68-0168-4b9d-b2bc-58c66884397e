import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:signals/signals.dart';
import 'package:video_player/video_player.dart';
import '../model/player_state.dart';
import '../model/danmaku.dart';
import '../utils/danmaku_api_utils.dart';
import '../utils/crypto_utils.dart';
import 'history.dart';

/// 视频播放服务
/// 封装VideoPlayerController，提供状态管理和播放控制
/// 集成弹幕功能和历史记录
class VideoPlayerService {
  VideoPlayerController? _controller;
  final HistoryService _historyService = GetIt.I<HistoryService>();

  // 播放器状态信号
  final Signal<PlayerState> _playerState = Signal(PlayerState.idle);
  final Signal<Duration> _position = Signal(Duration.zero);
  final Signal<Duration> _duration = Signal(Duration.zero);
  final Signal<Duration> _targetPosition = Signal(Duration.zero);
  final Signal<Duration> _bufferedPosition = Signal(Duration.zero);
  final Signal<double> _playbackSpeed = Signal(1.0);
  final Signal<String?> _errorMessage = Signal(null);

  // 弹幕相关信号
  final Signal<List<Danmaku>> _danmakus = Signal(<Danmaku>[]);
  final Signal<DanmakuSettings> _danmakuSettings = Signal(
    const DanmakuSettings(),
  );
  final Signal<bool> _isDanmakuLoading = Signal(false);

  // 当前视频信息
  String? _currentVideoPath;
  Timer? _positionTimer;

  // 性能统计
  DateTime? _initStartTime;
  DateTime? _firstFrameTime;

  /// 播放器状态
  ReadonlySignal<PlayerState> get playerState => _playerState.readonly();

  /// 当前播放位置
  ReadonlySignal<Duration> get position => _position.readonly();

  /// 视频总长度
  ReadonlySignal<Duration> get duration => _duration.readonly();

  /// 目标播放位置（跳转目标）
  ReadonlySignal<Duration> get targetPosition => _targetPosition.readonly();

  /// 已缓冲位置
  ReadonlySignal<Duration> get bufferedPosition => _bufferedPosition.readonly();

  /// 播放速度
  ReadonlySignal<double> get playbackSpeed => _playbackSpeed.readonly();

  /// 错误信息
  ReadonlySignal<String?> get errorMessage => _errorMessage.readonly();

  /// 弹幕列表
  ReadonlySignal<List<Danmaku>> get danmakus => _danmakus.readonly();

  /// 弹幕设置
  ReadonlySignal<DanmakuSettings> get danmakuSettings =>
      _danmakuSettings.readonly();

  /// 弹幕加载状态
  ReadonlySignal<bool> get isDanmakuLoading => _isDanmakuLoading.readonly();

  /// 视频播放器控制器
  VideoPlayerController? get controller => _controller;

  /// 当前视频路径
  String? get currentVideoPath => _currentVideoPath;

  /// 是否已初始化
  bool get isInitialized => _controller?.value.isInitialized ?? false;

  /// 初始化视频播放器
  Future<void> initialize({
    required String videoPath,
    Map<String, String>? headers,
  }) async {
    try {
      _initStartTime = DateTime.now();
      _playerState.value = PlayerState.loading;
      _errorMessage.value = null;
      _currentVideoPath = videoPath;

      // 释放之前的控制器
      await _disposeController();

      // 创建新的控制器
      if (videoPath.startsWith('http://') || videoPath.startsWith('https://')) {
        // 网络视频
        _controller = VideoPlayerController.networkUrl(
          Uri.parse(videoPath),
          httpHeaders: headers ?? {},
        );
      } else {
        // 本地文件
        _controller = VideoPlayerController.file(File(videoPath));
      }

      // 添加监听器
      _controller!.addListener(_onVideoPlayerUpdate);

      // 初始化控制器
      await _controller!.initialize();

      // 更新状态
      _playerState.value = PlayerState.ready;
      _duration.value = _controller!.value.duration;

      // 记录初始化完成时间
      if (_initStartTime != null) {
        final initDuration = DateTime.now().difference(_initStartTime!);
        debugPrint('视频初始化耗时: ${initDuration.inMilliseconds}ms');
      }

      // 开始历史记录
      await _historyService.startRecording(
        videoPath: videoPath,
        url: videoPath,
        headers: jsonEncode(headers ?? {}),
      );

      // 恢复播放进度
      await _restorePlaybackProgress();

      // 开始位置定时器
      _startPositionTimer();

      // 加载弹幕
      _loadDanmaku();
    } catch (e) {
      _playerState.value = PlayerState.error;
      _errorMessage.value = e.toString();
      rethrow;
    }
  }

  /// 播放视频
  Future<void> play() async {
    if (_controller != null && _playerState.value.canPlay) {
      await _controller!.play();
      _playerState.value = PlayerState.playing;
    }
  }

  /// 暂停视频
  Future<void> pause() async {
    if (_controller != null && _playerState.value.canPause) {
      await _controller!.pause();
      _playerState.value = PlayerState.paused;
    }
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    if (_controller != null && _controller!.value.isInitialized) {
      _targetPosition.value = position;
      await _controller!.seekTo(position);

      // 更新历史记录
      await _historyService.updateProgress(
        position: position,
        duration: _duration.value,
      );
    }
  }

  /// 设置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    if (_controller != null && _controller!.value.isInitialized) {
      await _controller!.setPlaybackSpeed(speed);
      _playbackSpeed.value = speed;
    }
  }

  /// 切换播放/暂停
  Future<void> togglePlayPause() async {
    if (_playerState.value.isPlaying) {
      await pause();
    } else if (_playerState.value.canPlay) {
      await play();
    }
  }

  /// 视频播放器状态更新回调
  void _onVideoPlayerUpdate() {
    if (_controller == null) return;

    final value = _controller!.value;

    // 更新播放位置
    _position.value = value.position;

    // 更新缓冲位置
    if (value.buffered.isNotEmpty) {
      _bufferedPosition.value = value.buffered.last.end;
    }

    // 更新播放状态
    if (value.hasError) {
      _playerState.value = PlayerState.error;
      _errorMessage.value = value.errorDescription;
    } else if (value.isBuffering) {
      _playerState.value = PlayerState.buffering;
    } else if (value.isPlaying) {
      _playerState.value = PlayerState.playing;

      // 记录首帧播放时间
      if (_firstFrameTime == null && _initStartTime != null) {
        _firstFrameTime = DateTime.now();
        final firstFrameDuration = _firstFrameTime!.difference(_initStartTime!);
        debugPrint('首帧播放耗时: ${firstFrameDuration.inMilliseconds}ms');
      }
    } else if (value.isInitialized) {
      _playerState.value = PlayerState.paused;
    }
  }

  /// 开始位置定时器
  void _startPositionTimer() {
    _positionTimer?.cancel();
    _positionTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (_controller != null && _controller!.value.isInitialized) {
        _position.value = _controller!.value.position;
      }
    });
  }

  /// 更新播放历史记录（由外部定时调用）
  Future<void> updatePlaybackHistory() async {
    if (_currentVideoPath != null) {
      await _historyService.updateProgress(
        position: _position.value,
        duration: _duration.value,
      );
    }
  }

  /// 恢复播放进度
  Future<void> _restorePlaybackProgress() async {
    if (_currentVideoPath == null) return;

    final history = await _historyService.getPlaybackHistory(
      _currentVideoPath!,
    );
    if (history != null && history.position > 0) {
      final position = Duration(milliseconds: history.position);
      await seekTo(position);
    }
  }

  /// 释放控制器
  Future<void> _disposeController() async {
    _positionTimer?.cancel();
    _positionTimer = null;

    if (_controller != null) {
      _controller!.removeListener(_onVideoPlayerUpdate);
      await _controller!.dispose();
      _controller = null;
    }
  }

  /// 加载弹幕
  Future<void> _loadDanmaku({bool force = false}) async {
    if (_currentVideoPath == null) return;

    _isDanmakuLoading.value = true;

    try {
      // 1. 检查本地缓存 (非强制刷新时)
      if (!force) {
        final cachedDanmakus = await _getCachedDanmakus(_currentVideoPath!);
        if (cachedDanmakus.isNotEmpty) {
          _danmakus.value = cachedDanmakus;
          debugPrint('从缓存加载弹幕: ${cachedDanmakus.length}条');
          _isDanmakuLoading.value = false;
          return;
        }
      }

      // 2. 如果没有缓存或强制刷新，尝试从弹弹play API获取（带重试机制）
      final danmakus = await _fetchDanmakusWithRetry(_currentVideoPath!);

      // 3. 保存到本地缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakusToCache(_currentVideoPath!, danmakus);
      }

      // 4. 更新弹幕列表
      _danmakus.value = danmakus;
      debugPrint('从API加载弹幕: ${danmakus.length}条');
    } catch (e) {
      debugPrint('加载弹幕失败: $e');
      // 加载失败时设置空列表，避免界面异常
      _danmakus.value = [];
    } finally {
      _isDanmakuLoading.value = false;
    }
  }

  /// 从缓存获取弹幕数据
  Future<List<Danmaku>> _getCachedDanmakus(String videoPath) async {
    try {
      // 获取播放历史记录，检查是否有缓存的弹幕路径
      final history = await _historyService.getPlaybackHistory(videoPath);
      if (history == null || history.danmakuPath.isEmpty) {
        return [];
      }

      // 检查弹幕文件是否存在
      final danmakuFile = File(history.danmakuPath);
      if (!await danmakuFile.exists()) {
        return [];
      }

      // 读取并解析弹幕文件
      final jsonString = await danmakuFile.readAsString();
      final jsonData = jsonDecode(jsonString) as List;

      return jsonData
          .map((item) => Danmaku.fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('读取缓存弹幕失败: $e');
      return [];
    }
  }

  /// 带重试机制的弹幕获取
  Future<List<Danmaku>> _fetchDanmakusWithRetry(String videoPath) async {
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('尝试获取弹幕，第$attempt次');
        return await _fetchDanmakusFromApi(videoPath);
      } catch (e) {
        debugPrint('第$attempt次获取弹幕失败: $e');

        // 检查是否为网络错误
        if (_isNetworkError(e)) {
          debugPrint('检测到网络错误，将重试');
        } else {
          debugPrint('非网络错误，停止重试');
          break; // 非网络错误，不重试
        }

        if (attempt == maxRetries) {
          rethrow; // 最后一次尝试失败，抛出异常
        }

        // 等待后重试
        await Future.delayed(retryDelay);
      }
    }

    return []; // 重试失败或非网络错误
  }

  /// 检查是否为网络错误
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket') ||
        errorString.contains('dns') ||
        errorString.contains('unreachable');
  }

  /// 从弹弹play API获取弹幕数据
  Future<List<Danmaku>> _fetchDanmakusFromApi(String videoPath) async {
    try {
      // 计算视频文件信息
      final videoInfo = await _getVideoInfo(videoPath);
      if (videoInfo == null) {
        debugPrint('无法获取视频信息');
        return [];
      }

      // 调用弹弹play API匹配视频
      final episodes = await DanmakuApiUtils.matchVideo(
        fileName: videoInfo['fileName'],
        fileHash: videoInfo['fileHash'],
        fileSize: videoInfo['fileSize'],
        duration: videoInfo['duration'],
      );

      if (episodes.isEmpty) {
        debugPrint('未找到匹配的节目');
        return [];
      }

      // 获取第一个匹配结果的弹幕
      final episode = episodes.first;
      final comments = await DanmakuApiUtils.getComments(episode.episodeId);

      // 转换为内部弹幕格式
      return comments.map((comment) => comment.toDanmaku()).toList();
    } catch (e) {
      debugPrint('从API获取弹幕失败: $e');
      return [];
    }
  }

  /// 保存弹幕数据到缓存
  Future<void> _saveDanmakusToCache(
    String videoPath,
    List<Danmaku> danmakus,
  ) async {
    try {
      // 生成缓存文件路径
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final cacheDir = Directory('${Directory.systemTemp.path}/danmaku_cache');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('${cacheDir.path}/$uniqueKey.json');

      // 将弹幕数据转换为JSON并保存
      final jsonData = danmakus.map((danmaku) => danmaku.toJson()).toList();
      await cacheFile.writeAsString(jsonEncode(jsonData));

      // 更新播放历史记录中的弹幕路径
      await _historyService.saveDanmakuPath(
        videoPath: videoPath,
        danmakuPath: cacheFile.path,
      );

      debugPrint('弹幕缓存保存成功: ${cacheFile.path}, 弹幕数量: ${danmakus.length}');

      // 清理过期的缓存文件（保留最近30天）
      _cleanupOldCacheFiles(cacheDir);
    } catch (e) {
      debugPrint('保存弹幕缓存失败: $e');
    }
  }

  /// 清理过期的缓存文件
  void _cleanupOldCacheFiles(Directory cacheDir) {
    try {
      final now = DateTime.now();
      final files = cacheDir.listSync();

      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          final stat = file.statSync();
          final age = now.difference(stat.modified);

          // 删除超过30天的缓存文件
          if (age.inDays > 30) {
            file.deleteSync();
            debugPrint('删除过期缓存文件: ${file.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('清理缓存文件失败: $e');
    }
  }

  /// 获取视频文件信息
  Future<Map<String, dynamic>?> _getVideoInfo(String videoPath) async {
    try {
      if (videoPath.startsWith('http://') || videoPath.startsWith('https://')) {
        // 网络视频：使用URL作为标识
        return {
          'fileName': Uri.parse(videoPath).pathSegments.last,
          'fileHash': CryptoUtils.calculateNetworkVideoHash(videoPath),
          'fileSize': 0, // 网络视频无法获取准确大小
          'duration': _duration.value.inSeconds,
        };
      } else {
        // 本地文件：计算实际文件信息
        final file = File(videoPath);
        if (!await file.exists()) {
          return null;
        }

        final fileSize = await file.length();
        final fileHash = await CryptoUtils.calculateVideoHash(videoPath);
        final fileName = file.path.split('/').last;

        return {
          'fileName': fileName,
          'fileHash': fileHash,
          'fileSize': fileSize,
          'duration': _duration.value.inSeconds,
        };
      }
    } catch (e) {
      debugPrint('获取视频信息失败: $e');
      return null;
    }
  }

  /// 检查并更新弹幕数据（自动刷新）
  Future<void> refreshDanmakus() async {
    if (_currentVideoPath == null) return;

    try {
      // 检查缓存是否过期（例如：超过72小时）
      final history = await _historyService.getPlaybackHistory(
        _currentVideoPath!,
      );
      if (history != null && history.danmakuUpdateTime > 0) {
        final lastUpdate = DateTime.fromMillisecondsSinceEpoch(
          history.danmakuUpdateTime,
        );
        final now = DateTime.now();
        final difference = now.difference(lastUpdate);

        // 如果缓存时间小于72小时，不需要更新
        if (difference.inHours < 72) {
          debugPrint('弹幕缓存仍然有效，跳过自动更新');
          return;
        }
      }

      // 强制重新加载弹幕
      debugPrint('弹幕缓存已过期，自动重新加载');
      await _loadDanmaku(force: true);
    } catch (e) {
      debugPrint('自动刷新弹幕失败: $e');
    }
  }

  /// 强制刷新弹幕（手动）
  Future<void> forceRefreshDanmakus() async {
    if (_currentVideoPath == null) {
      debugPrint('没有可刷新的视频');
      return;
    }
    debugPrint('开始手动刷新弹幕...');
    // 不在此处显示UI通知，应由UI层负责
    await _loadDanmaku(force: true);
  }

  /// 手动匹配并加载弹幕
  /// 在当前实现中，手动匹配和强制刷新的后端逻辑是一样的，都是重新走一遍API流程
  Future<void> forceMatchAndLoadDanmaku() async {
    // UI上可能会有不同的表现（例如弹窗），但调用的核心服务是同一个
    await forceRefreshDanmakus();
  }

  /// 更新弹幕设置
  void updateDanmakuSettings(DanmakuSettings settings) {
    _danmakuSettings.value = settings;
  }

  /// 从本地文件加载弹幕
  Future<bool> loadDanmakuFromFile(String filePath) async {
    if (_currentVideoPath == null) {
      debugPrint('无法加载弹幕，因为没有正在播放的视频');
      return false;
    }
    // 检查文件类型，目前仅支持json
    if (!filePath.toLowerCase().endsWith('.json')) {
      debugPrint('不支持的弹幕文件格式，仅支持 .json');
      return false;
    }

    try {
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('弹幕文件不存在: $filePath');
        return false;
      }

      // 读取并解析弹幕文件 (逻辑与 _getCachedDanmakus 类似)
      final jsonString = await file.readAsString();
      final jsonData = jsonDecode(jsonString) as List;
      final newDanmakus =
          jsonData
              .map((item) => Danmaku.fromJson(item as Map<String, dynamic>))
              .toList();

      // 更新弹幕列表
      _danmakus.value = newDanmakus;
      debugPrint('从本地文件加载弹幕成功: ${newDanmakus.length}条');

      // 将这个“本地”弹幕保存到缓存，以便统一管理
      await _saveDanmakusToCache(_currentVideoPath!, newDanmakus);

      return true;
    } catch (e) {
      debugPrint('从本地文件加载弹幕失败: $e');
      // 失败时清空弹幕
      _danmakus.value = [];
      return false;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    _historyService.stopRecording();
    await _disposeController();

    // 释放信号
    _playerState.dispose();
    _position.dispose();
    _duration.dispose();
    _targetPosition.dispose();
    _bufferedPosition.dispose();
    _playbackSpeed.dispose();
    _errorMessage.dispose();
    _danmakus.dispose();
    _danmakuSettings.dispose();
    _isDanmakuLoading.dispose();

    // 清理当前视频路径
    _currentVideoPath = null;
  }
}
