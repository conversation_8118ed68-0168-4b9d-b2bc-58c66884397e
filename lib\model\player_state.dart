/// 播放器状态枚举
enum PlayerState {
  /// 空闲状态
  idle,
  /// 加载中
  loading,
  /// 准备就绪
  ready,
  /// 播放中
  playing,
  /// 暂停
  paused,
  /// 缓冲中
  buffering,
  /// 错误状态
  error,
}

/// 播放器状态扩展方法
extension PlayerStateExtension on PlayerState {
  /// 是否正在播放
  bool get isPlaying => this == PlayerState.playing;
  
  /// 是否暂停
  bool get isPaused => this == PlayerState.paused;
  
  /// 是否加载中
  bool get isLoading => this == PlayerState.loading;
  
  /// 是否缓冲中
  bool get isBuffering => this == PlayerState.buffering;
  
  /// 是否有错误
  bool get hasError => this == PlayerState.error;
  
  /// 是否可以播放
  bool get canPlay => this == PlayerState.ready || this == PlayerState.paused;
  
  /// 是否可以暂停
  bool get canPause => this == PlayerState.playing;
}
