// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'storage.dart';

// ignore_for_file: type=lint
class $MediaLibrariesTable extends MediaLibraries
    with TableInfo<$MediaLibrariesTable, MediaLibrary> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $MediaLibrariesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
    'name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  @override
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
    'url',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _headersMeta = const VerificationMeta(
    'headers',
  );
  @override
  late final GeneratedColumn<String> headers = GeneratedColumn<String>(
    'headers',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  @override
  late final GeneratedColumnWithTypeConverter<MediaType, int> mediaType =
      GeneratedColumn<int>(
        'media_type',
        aliasedName,
        false,
        type: DriftSqlType.int,
        requiredDuringInsert: true,
      ).withConverter<MediaType>($MediaLibrariesTable.$convertermediaType);
  static const VerificationMeta _accountMeta = const VerificationMeta(
    'account',
  );
  @override
  late final GeneratedColumn<String> account = GeneratedColumn<String>(
    'account',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _passwordMeta = const VerificationMeta(
    'password',
  );
  @override
  late final GeneratedColumn<String> password = GeneratedColumn<String>(
    'password',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _isAnonymousMeta = const VerificationMeta(
    'isAnonymous',
  );
  @override
  late final GeneratedColumn<bool> isAnonymous = GeneratedColumn<bool>(
    'is_anonymous',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("is_anonymous" IN (0, 1))',
    ),
    defaultValue: const Constant(false),
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    name,
    url,
    headers,
    mediaType,
    account,
    password,
    isAnonymous,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'media_libraries';
  @override
  VerificationContext validateIntegrity(
    Insertable<MediaLibrary> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
        _nameMeta,
        name.isAcceptableOrUnknown(data['name']!, _nameMeta),
      );
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('url')) {
      context.handle(
        _urlMeta,
        url.isAcceptableOrUnknown(data['url']!, _urlMeta),
      );
    } else if (isInserting) {
      context.missing(_urlMeta);
    }
    if (data.containsKey('headers')) {
      context.handle(
        _headersMeta,
        headers.isAcceptableOrUnknown(data['headers']!, _headersMeta),
      );
    } else if (isInserting) {
      context.missing(_headersMeta);
    }
    if (data.containsKey('account')) {
      context.handle(
        _accountMeta,
        account.isAcceptableOrUnknown(data['account']!, _accountMeta),
      );
    }
    if (data.containsKey('password')) {
      context.handle(
        _passwordMeta,
        password.isAcceptableOrUnknown(data['password']!, _passwordMeta),
      );
    }
    if (data.containsKey('is_anonymous')) {
      context.handle(
        _isAnonymousMeta,
        isAnonymous.isAcceptableOrUnknown(
          data['is_anonymous']!,
          _isAnonymousMeta,
        ),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MediaLibrary map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MediaLibrary(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      name:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}name'],
          )!,
      url:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}url'],
          )!,
      headers:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}headers'],
          )!,
      mediaType: $MediaLibrariesTable.$convertermediaType.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}media_type'],
        )!,
      ),
      account: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}account'],
      ),
      password: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}password'],
      ),
      isAnonymous:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}is_anonymous'],
          )!,
    );
  }

  @override
  $MediaLibrariesTable createAlias(String alias) {
    return $MediaLibrariesTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<MediaType, int, int> $convertermediaType =
      const EnumIndexConverter<MediaType>(MediaType.values);
}

class MediaLibrary extends DataClass implements Insertable<MediaLibrary> {
  final int id;
  final String name;
  final String url;
  final String headers;
  final MediaType mediaType;
  final String? account;
  final String? password;
  final bool isAnonymous;
  const MediaLibrary({
    required this.id,
    required this.name,
    required this.url,
    required this.headers,
    required this.mediaType,
    this.account,
    this.password,
    required this.isAnonymous,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['url'] = Variable<String>(url);
    map['headers'] = Variable<String>(headers);
    {
      map['media_type'] = Variable<int>(
        $MediaLibrariesTable.$convertermediaType.toSql(mediaType),
      );
    }
    if (!nullToAbsent || account != null) {
      map['account'] = Variable<String>(account);
    }
    if (!nullToAbsent || password != null) {
      map['password'] = Variable<String>(password);
    }
    map['is_anonymous'] = Variable<bool>(isAnonymous);
    return map;
  }

  MediaLibrariesCompanion toCompanion(bool nullToAbsent) {
    return MediaLibrariesCompanion(
      id: Value(id),
      name: Value(name),
      url: Value(url),
      headers: Value(headers),
      mediaType: Value(mediaType),
      account:
          account == null && nullToAbsent
              ? const Value.absent()
              : Value(account),
      password:
          password == null && nullToAbsent
              ? const Value.absent()
              : Value(password),
      isAnonymous: Value(isAnonymous),
    );
  }

  factory MediaLibrary.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MediaLibrary(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      url: serializer.fromJson<String>(json['url']),
      headers: serializer.fromJson<String>(json['headers']),
      mediaType: $MediaLibrariesTable.$convertermediaType.fromJson(
        serializer.fromJson<int>(json['mediaType']),
      ),
      account: serializer.fromJson<String?>(json['account']),
      password: serializer.fromJson<String?>(json['password']),
      isAnonymous: serializer.fromJson<bool>(json['isAnonymous']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'url': serializer.toJson<String>(url),
      'headers': serializer.toJson<String>(headers),
      'mediaType': serializer.toJson<int>(
        $MediaLibrariesTable.$convertermediaType.toJson(mediaType),
      ),
      'account': serializer.toJson<String?>(account),
      'password': serializer.toJson<String?>(password),
      'isAnonymous': serializer.toJson<bool>(isAnonymous),
    };
  }

  MediaLibrary copyWith({
    int? id,
    String? name,
    String? url,
    String? headers,
    MediaType? mediaType,
    Value<String?> account = const Value.absent(),
    Value<String?> password = const Value.absent(),
    bool? isAnonymous,
  }) => MediaLibrary(
    id: id ?? this.id,
    name: name ?? this.name,
    url: url ?? this.url,
    headers: headers ?? this.headers,
    mediaType: mediaType ?? this.mediaType,
    account: account.present ? account.value : this.account,
    password: password.present ? password.value : this.password,
    isAnonymous: isAnonymous ?? this.isAnonymous,
  );
  MediaLibrary copyWithCompanion(MediaLibrariesCompanion data) {
    return MediaLibrary(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      url: data.url.present ? data.url.value : this.url,
      headers: data.headers.present ? data.headers.value : this.headers,
      mediaType: data.mediaType.present ? data.mediaType.value : this.mediaType,
      account: data.account.present ? data.account.value : this.account,
      password: data.password.present ? data.password.value : this.password,
      isAnonymous:
          data.isAnonymous.present ? data.isAnonymous.value : this.isAnonymous,
    );
  }

  @override
  String toString() {
    return (StringBuffer('MediaLibrary(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('url: $url, ')
          ..write('headers: $headers, ')
          ..write('mediaType: $mediaType, ')
          ..write('account: $account, ')
          ..write('password: $password, ')
          ..write('isAnonymous: $isAnonymous')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    name,
    url,
    headers,
    mediaType,
    account,
    password,
    isAnonymous,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MediaLibrary &&
          other.id == this.id &&
          other.name == this.name &&
          other.url == this.url &&
          other.headers == this.headers &&
          other.mediaType == this.mediaType &&
          other.account == this.account &&
          other.password == this.password &&
          other.isAnonymous == this.isAnonymous);
}

class MediaLibrariesCompanion extends UpdateCompanion<MediaLibrary> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> url;
  final Value<String> headers;
  final Value<MediaType> mediaType;
  final Value<String?> account;
  final Value<String?> password;
  final Value<bool> isAnonymous;
  const MediaLibrariesCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.url = const Value.absent(),
    this.headers = const Value.absent(),
    this.mediaType = const Value.absent(),
    this.account = const Value.absent(),
    this.password = const Value.absent(),
    this.isAnonymous = const Value.absent(),
  });
  MediaLibrariesCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String url,
    required String headers,
    required MediaType mediaType,
    this.account = const Value.absent(),
    this.password = const Value.absent(),
    this.isAnonymous = const Value.absent(),
  }) : name = Value(name),
       url = Value(url),
       headers = Value(headers),
       mediaType = Value(mediaType);
  static Insertable<MediaLibrary> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? url,
    Expression<String>? headers,
    Expression<int>? mediaType,
    Expression<String>? account,
    Expression<String>? password,
    Expression<bool>? isAnonymous,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (url != null) 'url': url,
      if (headers != null) 'headers': headers,
      if (mediaType != null) 'media_type': mediaType,
      if (account != null) 'account': account,
      if (password != null) 'password': password,
      if (isAnonymous != null) 'is_anonymous': isAnonymous,
    });
  }

  MediaLibrariesCompanion copyWith({
    Value<int>? id,
    Value<String>? name,
    Value<String>? url,
    Value<String>? headers,
    Value<MediaType>? mediaType,
    Value<String?>? account,
    Value<String?>? password,
    Value<bool>? isAnonymous,
  }) {
    return MediaLibrariesCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      headers: headers ?? this.headers,
      mediaType: mediaType ?? this.mediaType,
      account: account ?? this.account,
      password: password ?? this.password,
      isAnonymous: isAnonymous ?? this.isAnonymous,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (headers.present) {
      map['headers'] = Variable<String>(headers.value);
    }
    if (mediaType.present) {
      map['media_type'] = Variable<int>(
        $MediaLibrariesTable.$convertermediaType.toSql(mediaType.value),
      );
    }
    if (account.present) {
      map['account'] = Variable<String>(account.value);
    }
    if (password.present) {
      map['password'] = Variable<String>(password.value);
    }
    if (isAnonymous.present) {
      map['is_anonymous'] = Variable<bool>(isAnonymous.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MediaLibrariesCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('url: $url, ')
          ..write('headers: $headers, ')
          ..write('mediaType: $mediaType, ')
          ..write('account: $account, ')
          ..write('password: $password, ')
          ..write('isAnonymous: $isAnonymous')
          ..write(')'))
        .toString();
  }
}

class $HistoriesTable extends Histories
    with TableInfo<$HistoriesTable, History> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $HistoriesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _uniqueKeyMeta = const VerificationMeta(
    'uniqueKey',
  );
  @override
  late final GeneratedColumn<String> uniqueKey = GeneratedColumn<String>(
    'unique_key',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'),
  );
  static const VerificationMeta _durationMeta = const VerificationMeta(
    'duration',
  );
  @override
  late final GeneratedColumn<int> duration = GeneratedColumn<int>(
    'duration',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _positionMeta = const VerificationMeta(
    'position',
  );
  @override
  late final GeneratedColumn<int> position = GeneratedColumn<int>(
    'position',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  @override
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
    'url',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _headersMeta = const VerificationMeta(
    'headers',
  );
  @override
  late final GeneratedColumn<String> headers = GeneratedColumn<String>(
    'headers',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _updateTimeMeta = const VerificationMeta(
    'updateTime',
  );
  @override
  late final GeneratedColumn<int> updateTime = GeneratedColumn<int>(
    'update_time',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _snapshotMeta = const VerificationMeta(
    'snapshot',
  );
  @override
  late final GeneratedColumn<String> snapshot = GeneratedColumn<String>(
    'snapshot',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _danmakuPathMeta = const VerificationMeta(
    'danmakuPath',
  );
  @override
  late final GeneratedColumn<String> danmakuPath = GeneratedColumn<String>(
    'danmaku_path',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _danmakuUpdateTimeMeta = const VerificationMeta(
    'danmakuUpdateTime',
  );
  @override
  late final GeneratedColumn<int> danmakuUpdateTime = GeneratedColumn<int>(
    'danmaku_update_time',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    uniqueKey,
    duration,
    position,
    url,
    headers,
    updateTime,
    snapshot,
    danmakuPath,
    danmakuUpdateTime,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'histories';
  @override
  VerificationContext validateIntegrity(
    Insertable<History> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('unique_key')) {
      context.handle(
        _uniqueKeyMeta,
        uniqueKey.isAcceptableOrUnknown(data['unique_key']!, _uniqueKeyMeta),
      );
    } else if (isInserting) {
      context.missing(_uniqueKeyMeta);
    }
    if (data.containsKey('duration')) {
      context.handle(
        _durationMeta,
        duration.isAcceptableOrUnknown(data['duration']!, _durationMeta),
      );
    } else if (isInserting) {
      context.missing(_durationMeta);
    }
    if (data.containsKey('position')) {
      context.handle(
        _positionMeta,
        position.isAcceptableOrUnknown(data['position']!, _positionMeta),
      );
    } else if (isInserting) {
      context.missing(_positionMeta);
    }
    if (data.containsKey('url')) {
      context.handle(
        _urlMeta,
        url.isAcceptableOrUnknown(data['url']!, _urlMeta),
      );
    } else if (isInserting) {
      context.missing(_urlMeta);
    }
    if (data.containsKey('headers')) {
      context.handle(
        _headersMeta,
        headers.isAcceptableOrUnknown(data['headers']!, _headersMeta),
      );
    } else if (isInserting) {
      context.missing(_headersMeta);
    }
    if (data.containsKey('update_time')) {
      context.handle(
        _updateTimeMeta,
        updateTime.isAcceptableOrUnknown(data['update_time']!, _updateTimeMeta),
      );
    } else if (isInserting) {
      context.missing(_updateTimeMeta);
    }
    if (data.containsKey('snapshot')) {
      context.handle(
        _snapshotMeta,
        snapshot.isAcceptableOrUnknown(data['snapshot']!, _snapshotMeta),
      );
    } else if (isInserting) {
      context.missing(_snapshotMeta);
    }
    if (data.containsKey('danmaku_path')) {
      context.handle(
        _danmakuPathMeta,
        danmakuPath.isAcceptableOrUnknown(
          data['danmaku_path']!,
          _danmakuPathMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_danmakuPathMeta);
    }
    if (data.containsKey('danmaku_update_time')) {
      context.handle(
        _danmakuUpdateTimeMeta,
        danmakuUpdateTime.isAcceptableOrUnknown(
          data['danmaku_update_time']!,
          _danmakuUpdateTimeMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_danmakuUpdateTimeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  History map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return History(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      uniqueKey:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}unique_key'],
          )!,
      duration:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}duration'],
          )!,
      position:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}position'],
          )!,
      url:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}url'],
          )!,
      headers:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}headers'],
          )!,
      updateTime:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}update_time'],
          )!,
      snapshot:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}snapshot'],
          )!,
      danmakuPath:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}danmaku_path'],
          )!,
      danmakuUpdateTime:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}danmaku_update_time'],
          )!,
    );
  }

  @override
  $HistoriesTable createAlias(String alias) {
    return $HistoriesTable(attachedDatabase, alias);
  }
}

class History extends DataClass implements Insertable<History> {
  final int id;
  final String uniqueKey;
  final int duration;
  final int position;
  final String url;
  final String headers;
  final int updateTime;
  final String snapshot;
  final String danmakuPath;
  final int danmakuUpdateTime;
  const History({
    required this.id,
    required this.uniqueKey,
    required this.duration,
    required this.position,
    required this.url,
    required this.headers,
    required this.updateTime,
    required this.snapshot,
    required this.danmakuPath,
    required this.danmakuUpdateTime,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['unique_key'] = Variable<String>(uniqueKey);
    map['duration'] = Variable<int>(duration);
    map['position'] = Variable<int>(position);
    map['url'] = Variable<String>(url);
    map['headers'] = Variable<String>(headers);
    map['update_time'] = Variable<int>(updateTime);
    map['snapshot'] = Variable<String>(snapshot);
    map['danmaku_path'] = Variable<String>(danmakuPath);
    map['danmaku_update_time'] = Variable<int>(danmakuUpdateTime);
    return map;
  }

  HistoriesCompanion toCompanion(bool nullToAbsent) {
    return HistoriesCompanion(
      id: Value(id),
      uniqueKey: Value(uniqueKey),
      duration: Value(duration),
      position: Value(position),
      url: Value(url),
      headers: Value(headers),
      updateTime: Value(updateTime),
      snapshot: Value(snapshot),
      danmakuPath: Value(danmakuPath),
      danmakuUpdateTime: Value(danmakuUpdateTime),
    );
  }

  factory History.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return History(
      id: serializer.fromJson<int>(json['id']),
      uniqueKey: serializer.fromJson<String>(json['uniqueKey']),
      duration: serializer.fromJson<int>(json['duration']),
      position: serializer.fromJson<int>(json['position']),
      url: serializer.fromJson<String>(json['url']),
      headers: serializer.fromJson<String>(json['headers']),
      updateTime: serializer.fromJson<int>(json['updateTime']),
      snapshot: serializer.fromJson<String>(json['snapshot']),
      danmakuPath: serializer.fromJson<String>(json['danmakuPath']),
      danmakuUpdateTime: serializer.fromJson<int>(json['danmakuUpdateTime']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uniqueKey': serializer.toJson<String>(uniqueKey),
      'duration': serializer.toJson<int>(duration),
      'position': serializer.toJson<int>(position),
      'url': serializer.toJson<String>(url),
      'headers': serializer.toJson<String>(headers),
      'updateTime': serializer.toJson<int>(updateTime),
      'snapshot': serializer.toJson<String>(snapshot),
      'danmakuPath': serializer.toJson<String>(danmakuPath),
      'danmakuUpdateTime': serializer.toJson<int>(danmakuUpdateTime),
    };
  }

  History copyWith({
    int? id,
    String? uniqueKey,
    int? duration,
    int? position,
    String? url,
    String? headers,
    int? updateTime,
    String? snapshot,
    String? danmakuPath,
    int? danmakuUpdateTime,
  }) => History(
    id: id ?? this.id,
    uniqueKey: uniqueKey ?? this.uniqueKey,
    duration: duration ?? this.duration,
    position: position ?? this.position,
    url: url ?? this.url,
    headers: headers ?? this.headers,
    updateTime: updateTime ?? this.updateTime,
    snapshot: snapshot ?? this.snapshot,
    danmakuPath: danmakuPath ?? this.danmakuPath,
    danmakuUpdateTime: danmakuUpdateTime ?? this.danmakuUpdateTime,
  );
  History copyWithCompanion(HistoriesCompanion data) {
    return History(
      id: data.id.present ? data.id.value : this.id,
      uniqueKey: data.uniqueKey.present ? data.uniqueKey.value : this.uniqueKey,
      duration: data.duration.present ? data.duration.value : this.duration,
      position: data.position.present ? data.position.value : this.position,
      url: data.url.present ? data.url.value : this.url,
      headers: data.headers.present ? data.headers.value : this.headers,
      updateTime:
          data.updateTime.present ? data.updateTime.value : this.updateTime,
      snapshot: data.snapshot.present ? data.snapshot.value : this.snapshot,
      danmakuPath:
          data.danmakuPath.present ? data.danmakuPath.value : this.danmakuPath,
      danmakuUpdateTime:
          data.danmakuUpdateTime.present
              ? data.danmakuUpdateTime.value
              : this.danmakuUpdateTime,
    );
  }

  @override
  String toString() {
    return (StringBuffer('History(')
          ..write('id: $id, ')
          ..write('uniqueKey: $uniqueKey, ')
          ..write('duration: $duration, ')
          ..write('position: $position, ')
          ..write('url: $url, ')
          ..write('headers: $headers, ')
          ..write('updateTime: $updateTime, ')
          ..write('snapshot: $snapshot, ')
          ..write('danmakuPath: $danmakuPath, ')
          ..write('danmakuUpdateTime: $danmakuUpdateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    uniqueKey,
    duration,
    position,
    url,
    headers,
    updateTime,
    snapshot,
    danmakuPath,
    danmakuUpdateTime,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is History &&
          other.id == this.id &&
          other.uniqueKey == this.uniqueKey &&
          other.duration == this.duration &&
          other.position == this.position &&
          other.url == this.url &&
          other.headers == this.headers &&
          other.updateTime == this.updateTime &&
          other.snapshot == this.snapshot &&
          other.danmakuPath == this.danmakuPath &&
          other.danmakuUpdateTime == this.danmakuUpdateTime);
}

class HistoriesCompanion extends UpdateCompanion<History> {
  final Value<int> id;
  final Value<String> uniqueKey;
  final Value<int> duration;
  final Value<int> position;
  final Value<String> url;
  final Value<String> headers;
  final Value<int> updateTime;
  final Value<String> snapshot;
  final Value<String> danmakuPath;
  final Value<int> danmakuUpdateTime;
  const HistoriesCompanion({
    this.id = const Value.absent(),
    this.uniqueKey = const Value.absent(),
    this.duration = const Value.absent(),
    this.position = const Value.absent(),
    this.url = const Value.absent(),
    this.headers = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.snapshot = const Value.absent(),
    this.danmakuPath = const Value.absent(),
    this.danmakuUpdateTime = const Value.absent(),
  });
  HistoriesCompanion.insert({
    this.id = const Value.absent(),
    required String uniqueKey,
    required int duration,
    required int position,
    required String url,
    required String headers,
    required int updateTime,
    required String snapshot,
    required String danmakuPath,
    required int danmakuUpdateTime,
  }) : uniqueKey = Value(uniqueKey),
       duration = Value(duration),
       position = Value(position),
       url = Value(url),
       headers = Value(headers),
       updateTime = Value(updateTime),
       snapshot = Value(snapshot),
       danmakuPath = Value(danmakuPath),
       danmakuUpdateTime = Value(danmakuUpdateTime);
  static Insertable<History> custom({
    Expression<int>? id,
    Expression<String>? uniqueKey,
    Expression<int>? duration,
    Expression<int>? position,
    Expression<String>? url,
    Expression<String>? headers,
    Expression<int>? updateTime,
    Expression<String>? snapshot,
    Expression<String>? danmakuPath,
    Expression<int>? danmakuUpdateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uniqueKey != null) 'unique_key': uniqueKey,
      if (duration != null) 'duration': duration,
      if (position != null) 'position': position,
      if (url != null) 'url': url,
      if (headers != null) 'headers': headers,
      if (updateTime != null) 'update_time': updateTime,
      if (snapshot != null) 'snapshot': snapshot,
      if (danmakuPath != null) 'danmaku_path': danmakuPath,
      if (danmakuUpdateTime != null) 'danmaku_update_time': danmakuUpdateTime,
    });
  }

  HistoriesCompanion copyWith({
    Value<int>? id,
    Value<String>? uniqueKey,
    Value<int>? duration,
    Value<int>? position,
    Value<String>? url,
    Value<String>? headers,
    Value<int>? updateTime,
    Value<String>? snapshot,
    Value<String>? danmakuPath,
    Value<int>? danmakuUpdateTime,
  }) {
    return HistoriesCompanion(
      id: id ?? this.id,
      uniqueKey: uniqueKey ?? this.uniqueKey,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      url: url ?? this.url,
      headers: headers ?? this.headers,
      updateTime: updateTime ?? this.updateTime,
      snapshot: snapshot ?? this.snapshot,
      danmakuPath: danmakuPath ?? this.danmakuPath,
      danmakuUpdateTime: danmakuUpdateTime ?? this.danmakuUpdateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uniqueKey.present) {
      map['unique_key'] = Variable<String>(uniqueKey.value);
    }
    if (duration.present) {
      map['duration'] = Variable<int>(duration.value);
    }
    if (position.present) {
      map['position'] = Variable<int>(position.value);
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (headers.present) {
      map['headers'] = Variable<String>(headers.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<int>(updateTime.value);
    }
    if (snapshot.present) {
      map['snapshot'] = Variable<String>(snapshot.value);
    }
    if (danmakuPath.present) {
      map['danmaku_path'] = Variable<String>(danmakuPath.value);
    }
    if (danmakuUpdateTime.present) {
      map['danmaku_update_time'] = Variable<int>(danmakuUpdateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('HistoriesCompanion(')
          ..write('id: $id, ')
          ..write('uniqueKey: $uniqueKey, ')
          ..write('duration: $duration, ')
          ..write('position: $position, ')
          ..write('url: $url, ')
          ..write('headers: $headers, ')
          ..write('updateTime: $updateTime, ')
          ..write('snapshot: $snapshot, ')
          ..write('danmakuPath: $danmakuPath, ')
          ..write('danmakuUpdateTime: $danmakuUpdateTime')
          ..write(')'))
        .toString();
  }
}

abstract class _$StorageService extends GeneratedDatabase {
  _$StorageService(QueryExecutor e) : super(e);
  $StorageServiceManager get managers => $StorageServiceManager(this);
  late final $MediaLibrariesTable mediaLibraries = $MediaLibrariesTable(this);
  late final $HistoriesTable histories = $HistoriesTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
    mediaLibraries,
    histories,
  ];
}

typedef $$MediaLibrariesTableCreateCompanionBuilder =
    MediaLibrariesCompanion Function({
      Value<int> id,
      required String name,
      required String url,
      required String headers,
      required MediaType mediaType,
      Value<String?> account,
      Value<String?> password,
      Value<bool> isAnonymous,
    });
typedef $$MediaLibrariesTableUpdateCompanionBuilder =
    MediaLibrariesCompanion Function({
      Value<int> id,
      Value<String> name,
      Value<String> url,
      Value<String> headers,
      Value<MediaType> mediaType,
      Value<String?> account,
      Value<String?> password,
      Value<bool> isAnonymous,
    });

class $$MediaLibrariesTableFilterComposer
    extends Composer<_$StorageService, $MediaLibrariesTable> {
  $$MediaLibrariesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<MediaType, MediaType, int> get mediaType =>
      $composableBuilder(
        column: $table.mediaType,
        builder: (column) => ColumnWithTypeConverterFilters(column),
      );

  ColumnFilters<String> get account => $composableBuilder(
    column: $table.account,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get password => $composableBuilder(
    column: $table.password,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get isAnonymous => $composableBuilder(
    column: $table.isAnonymous,
    builder: (column) => ColumnFilters(column),
  );
}

class $$MediaLibrariesTableOrderingComposer
    extends Composer<_$StorageService, $MediaLibrariesTable> {
  $$MediaLibrariesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mediaType => $composableBuilder(
    column: $table.mediaType,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get account => $composableBuilder(
    column: $table.account,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get password => $composableBuilder(
    column: $table.password,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get isAnonymous => $composableBuilder(
    column: $table.isAnonymous,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$MediaLibrariesTableAnnotationComposer
    extends Composer<_$StorageService, $MediaLibrariesTable> {
  $$MediaLibrariesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get url =>
      $composableBuilder(column: $table.url, builder: (column) => column);

  GeneratedColumn<String> get headers =>
      $composableBuilder(column: $table.headers, builder: (column) => column);

  GeneratedColumnWithTypeConverter<MediaType, int> get mediaType =>
      $composableBuilder(column: $table.mediaType, builder: (column) => column);

  GeneratedColumn<String> get account =>
      $composableBuilder(column: $table.account, builder: (column) => column);

  GeneratedColumn<String> get password =>
      $composableBuilder(column: $table.password, builder: (column) => column);

  GeneratedColumn<bool> get isAnonymous => $composableBuilder(
    column: $table.isAnonymous,
    builder: (column) => column,
  );
}

class $$MediaLibrariesTableTableManager
    extends
        RootTableManager<
          _$StorageService,
          $MediaLibrariesTable,
          MediaLibrary,
          $$MediaLibrariesTableFilterComposer,
          $$MediaLibrariesTableOrderingComposer,
          $$MediaLibrariesTableAnnotationComposer,
          $$MediaLibrariesTableCreateCompanionBuilder,
          $$MediaLibrariesTableUpdateCompanionBuilder,
          (
            MediaLibrary,
            BaseReferences<
              _$StorageService,
              $MediaLibrariesTable,
              MediaLibrary
            >,
          ),
          MediaLibrary,
          PrefetchHooks Function()
        > {
  $$MediaLibrariesTableTableManager(
    _$StorageService db,
    $MediaLibrariesTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$MediaLibrariesTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () =>
                  $$MediaLibrariesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$MediaLibrariesTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String> name = const Value.absent(),
                Value<String> url = const Value.absent(),
                Value<String> headers = const Value.absent(),
                Value<MediaType> mediaType = const Value.absent(),
                Value<String?> account = const Value.absent(),
                Value<String?> password = const Value.absent(),
                Value<bool> isAnonymous = const Value.absent(),
              }) => MediaLibrariesCompanion(
                id: id,
                name: name,
                url: url,
                headers: headers,
                mediaType: mediaType,
                account: account,
                password: password,
                isAnonymous: isAnonymous,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required String name,
                required String url,
                required String headers,
                required MediaType mediaType,
                Value<String?> account = const Value.absent(),
                Value<String?> password = const Value.absent(),
                Value<bool> isAnonymous = const Value.absent(),
              }) => MediaLibrariesCompanion.insert(
                id: id,
                name: name,
                url: url,
                headers: headers,
                mediaType: mediaType,
                account: account,
                password: password,
                isAnonymous: isAnonymous,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$MediaLibrariesTableProcessedTableManager =
    ProcessedTableManager<
      _$StorageService,
      $MediaLibrariesTable,
      MediaLibrary,
      $$MediaLibrariesTableFilterComposer,
      $$MediaLibrariesTableOrderingComposer,
      $$MediaLibrariesTableAnnotationComposer,
      $$MediaLibrariesTableCreateCompanionBuilder,
      $$MediaLibrariesTableUpdateCompanionBuilder,
      (
        MediaLibrary,
        BaseReferences<_$StorageService, $MediaLibrariesTable, MediaLibrary>,
      ),
      MediaLibrary,
      PrefetchHooks Function()
    >;
typedef $$HistoriesTableCreateCompanionBuilder =
    HistoriesCompanion Function({
      Value<int> id,
      required String uniqueKey,
      required int duration,
      required int position,
      required String url,
      required String headers,
      required int updateTime,
      required String snapshot,
      required String danmakuPath,
      required int danmakuUpdateTime,
    });
typedef $$HistoriesTableUpdateCompanionBuilder =
    HistoriesCompanion Function({
      Value<int> id,
      Value<String> uniqueKey,
      Value<int> duration,
      Value<int> position,
      Value<String> url,
      Value<String> headers,
      Value<int> updateTime,
      Value<String> snapshot,
      Value<String> danmakuPath,
      Value<int> danmakuUpdateTime,
    });

class $$HistoriesTableFilterComposer
    extends Composer<_$StorageService, $HistoriesTable> {
  $$HistoriesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get uniqueKey => $composableBuilder(
    column: $table.uniqueKey,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get duration => $composableBuilder(
    column: $table.duration,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get position => $composableBuilder(
    column: $table.position,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get updateTime => $composableBuilder(
    column: $table.updateTime,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get snapshot => $composableBuilder(
    column: $table.snapshot,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get danmakuPath => $composableBuilder(
    column: $table.danmakuPath,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get danmakuUpdateTime => $composableBuilder(
    column: $table.danmakuUpdateTime,
    builder: (column) => ColumnFilters(column),
  );
}

class $$HistoriesTableOrderingComposer
    extends Composer<_$StorageService, $HistoriesTable> {
  $$HistoriesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get uniqueKey => $composableBuilder(
    column: $table.uniqueKey,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get duration => $composableBuilder(
    column: $table.duration,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get position => $composableBuilder(
    column: $table.position,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get updateTime => $composableBuilder(
    column: $table.updateTime,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get snapshot => $composableBuilder(
    column: $table.snapshot,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get danmakuPath => $composableBuilder(
    column: $table.danmakuPath,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get danmakuUpdateTime => $composableBuilder(
    column: $table.danmakuUpdateTime,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$HistoriesTableAnnotationComposer
    extends Composer<_$StorageService, $HistoriesTable> {
  $$HistoriesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get uniqueKey =>
      $composableBuilder(column: $table.uniqueKey, builder: (column) => column);

  GeneratedColumn<int> get duration =>
      $composableBuilder(column: $table.duration, builder: (column) => column);

  GeneratedColumn<int> get position =>
      $composableBuilder(column: $table.position, builder: (column) => column);

  GeneratedColumn<String> get url =>
      $composableBuilder(column: $table.url, builder: (column) => column);

  GeneratedColumn<String> get headers =>
      $composableBuilder(column: $table.headers, builder: (column) => column);

  GeneratedColumn<int> get updateTime => $composableBuilder(
    column: $table.updateTime,
    builder: (column) => column,
  );

  GeneratedColumn<String> get snapshot =>
      $composableBuilder(column: $table.snapshot, builder: (column) => column);

  GeneratedColumn<String> get danmakuPath => $composableBuilder(
    column: $table.danmakuPath,
    builder: (column) => column,
  );

  GeneratedColumn<int> get danmakuUpdateTime => $composableBuilder(
    column: $table.danmakuUpdateTime,
    builder: (column) => column,
  );
}

class $$HistoriesTableTableManager
    extends
        RootTableManager<
          _$StorageService,
          $HistoriesTable,
          History,
          $$HistoriesTableFilterComposer,
          $$HistoriesTableOrderingComposer,
          $$HistoriesTableAnnotationComposer,
          $$HistoriesTableCreateCompanionBuilder,
          $$HistoriesTableUpdateCompanionBuilder,
          (History, BaseReferences<_$StorageService, $HistoriesTable, History>),
          History,
          PrefetchHooks Function()
        > {
  $$HistoriesTableTableManager(_$StorageService db, $HistoriesTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$HistoriesTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$HistoriesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$HistoriesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String> uniqueKey = const Value.absent(),
                Value<int> duration = const Value.absent(),
                Value<int> position = const Value.absent(),
                Value<String> url = const Value.absent(),
                Value<String> headers = const Value.absent(),
                Value<int> updateTime = const Value.absent(),
                Value<String> snapshot = const Value.absent(),
                Value<String> danmakuPath = const Value.absent(),
                Value<int> danmakuUpdateTime = const Value.absent(),
              }) => HistoriesCompanion(
                id: id,
                uniqueKey: uniqueKey,
                duration: duration,
                position: position,
                url: url,
                headers: headers,
                updateTime: updateTime,
                snapshot: snapshot,
                danmakuPath: danmakuPath,
                danmakuUpdateTime: danmakuUpdateTime,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required String uniqueKey,
                required int duration,
                required int position,
                required String url,
                required String headers,
                required int updateTime,
                required String snapshot,
                required String danmakuPath,
                required int danmakuUpdateTime,
              }) => HistoriesCompanion.insert(
                id: id,
                uniqueKey: uniqueKey,
                duration: duration,
                position: position,
                url: url,
                headers: headers,
                updateTime: updateTime,
                snapshot: snapshot,
                danmakuPath: danmakuPath,
                danmakuUpdateTime: danmakuUpdateTime,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$HistoriesTableProcessedTableManager =
    ProcessedTableManager<
      _$StorageService,
      $HistoriesTable,
      History,
      $$HistoriesTableFilterComposer,
      $$HistoriesTableOrderingComposer,
      $$HistoriesTableAnnotationComposer,
      $$HistoriesTableCreateCompanionBuilder,
      $$HistoriesTableUpdateCompanionBuilder,
      (History, BaseReferences<_$StorageService, $HistoriesTable, History>),
      History,
      PrefetchHooks Function()
    >;

class $StorageServiceManager {
  final _$StorageService _db;
  $StorageServiceManager(this._db);
  $$MediaLibrariesTableTableManager get mediaLibraries =>
      $$MediaLibrariesTableTableManager(_db, _db.mediaLibraries);
  $$HistoriesTableTableManager get histories =>
      $$HistoriesTableTableManager(_db, _db.histories);
}
