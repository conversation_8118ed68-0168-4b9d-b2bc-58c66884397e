import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

class CryptoUtils {
  /// Generate unique key for video file based on its path
  static String generateVideoUniqueKey(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 计算视频文件的MD5哈希值（前16MB数据）
  /// 用于弹弹play API的文件识别
  static Future<String> calculateVideoHash(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('文件不存在', filePath);
      }

      // 读取前16MB数据
      const int maxBytes = 16 * 1024 * 1024; // 16MB
      final fileSize = await file.length();
      final bytesToRead = fileSize < maxBytes ? fileSize : maxBytes;

      final randomAccessFile = await file.open();
      final bytes = await randomAccessFile.read(bytesToRead);
      await randomAccessFile.close();

      // 计算MD5
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      throw Exception('计算视频哈希值失败: $e');
    }
  }

  /// 计算网络视频的唯一标识
  /// 对于网络视频，使用URL作为标识
  static String calculateNetworkVideoHash(String url) {
    final bytes = utf8.encode(url);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 生成弹弹play API签名
  static String generateApiSignature(
    String appId,
    int timestamp,
    String path,
    String appSecret,
  ) {
    final data = '$appId$timestamp$path$appSecret';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return base64.encode(digest.bytes);
  }
}
