import 'dart:io';

import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals/signals_flutter.dart';

class FileExplorerPage extends StatefulWidget {
  final int mediaLibraryId;
  const FileExplorerPage({super.key, required this.mediaLibraryId});

  @override
  State<FileExplorerPage> createState() => _FileExplorerPageState();
}

class _FileExplorerPageState extends State<FileExplorerPage> {
  MediaLibrary? _mediaLibrary;
  late FileExplorerService _fileExplorerService;

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    final mediaLibrary = await mediaLibraryService.getMediaLibrary(
      widget.mediaLibraryId,
    );
    if (mediaLibrary == null) {
      return;
    }
    late FileExplorerProvider provider;
    switch (mediaLibrary.mediaType) {
      case MediaType.webdav:
        provider = WebDAVFileExplorerProvider(mediaLibrary);
        break;
      case MediaType.ftp:
        break;
      case MediaType.smb:
        break;
      case MediaType.local:
        provider = LocalFileExplorerProvider();
        break;
    }
    _fileExplorerService = FileExplorerService(provider: provider);
    setState(() {
      _mediaLibrary = mediaLibrary;
    });
  }

  Future<Widget> _buildPerfix(History? history) async {
    if (history != null) {
      final directory = await getApplicationDocumentsDirectory();
      File image = File('${directory.path}/${history.snapshot}');
      if (await image.exists()) {
        return Image.file(image);
      }
    }
    return const Icon(FIcons.play);
  }

  void _playVideo(FileItem file) {
    context.push(
      '$videoPlayerPath?path=${Uri.encodeComponent('${_mediaLibrary!.url}${file.path}')}&headers=${Uri.encodeComponent(_mediaLibrary!.headers)}',
    );
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (!_fileExplorerService.goBack()) {
            Navigator.of(context).pop();
          }
        }
      },
      child: FScaffold(
        scaffoldStyle: context.theme.scaffoldStyle.copyWith(
          childPadding: EdgeInsets.all(0),
        ),
        header: FHeader(
          title: Row(
            children: [
              FButton.icon(
                style: FButtonStyle.ghost,
                onPress: () {
                  if (!_fileExplorerService.goBack()) {
                    Navigator.of(context).pop();
                  }
                },
                child: const Icon(FIcons.arrowLeft, size: 24),
              ),
              FButton.icon(
                style: FButtonStyle.ghost,
                onPress: Navigator.of(context).pop,
                child: const Icon(FIcons.x, size: 24),
              ),
              SizedBox(width: 10),
              _mediaLibrary == null
                  ? const Text('加载中...')
                  : Text(_mediaLibrary!.name),
            ],
          ),
        ),
        child:
            _mediaLibrary == null
                ? const Center(child: CircularProgressIndicator())
                : Watch(
                  (context) => FutureBuilder(
                    future: _fileExplorerService.files.future,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      if (snapshot.hasData) {
                        if (snapshot.data!.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  FIcons.folder,
                                  size: 48,
                                  color: context.theme.colors.mutedForeground,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  '此文件夹为空',
                                  style: context.theme.typography.lg,
                                ),
                              ],
                            ),
                          );
                        }
                        return FTileGroup(
                          divider: FTileDivider.indented,
                          style: tileGroupStyle(
                            colors: context.theme.colors,
                            typography: context.theme.typography,
                            style: context.theme.style,
                            newColors: context.theme.colors.copyWith(
                              border: const Color.fromARGB(0, 238, 238, 238),
                            ),
                          ),
                          children:
                              snapshot.data!
                                  .map(
                                    (FileItem file) =>
                                        file.isFolder
                                            ? FTile(
                                              prefixIcon: const Icon(
                                                FIcons.folder,
                                              ),
                                              title: Text(file.name),
                                              onPress:
                                                  () => {
                                                    _fileExplorerService.goDir(
                                                      file.name,
                                                    ),
                                                  },
                                            )
                                            : FTile(
                                              style: videoTileStyle(
                                                colors: context.theme.colors,
                                                typography:
                                                    context.theme.typography,
                                                style: context.theme.style,
                                                newColors: context.theme.colors
                                                    .copyWith(
                                                      border:
                                                          const Color.fromARGB(
                                                            0,
                                                            238,
                                                            238,
                                                            238,
                                                          ),
                                                    ),
                                              ),
                                              prefixIcon: FutureBuilder(
                                                future: _buildPerfix(
                                                  file.history,
                                                ),
                                                builder: (context, snapshot) {
                                                  if (snapshot
                                                          .connectionState ==
                                                      ConnectionState.waiting) {
                                                    return const CircularProgressIndicator();
                                                  }
                                                  if (snapshot.hasData) {
                                                    return file.isVideo
                                                        ? snapshot.data!
                                                        : const Icon(
                                                          FIcons.file,
                                                        );
                                                  }
                                                  return const Icon(
                                                    FIcons.file,
                                                  );
                                                },
                                              ),
                                              title: Text(
                                                file.name,
                                                maxLines: 2,
                                              ),
                                              subtitle:
                                                  file.history != null
                                                      ? Text(
                                                        '观看进度: ${_formatDuration(file.history!.position)}/${_formatDuration(file.history!.duration)}',
                                                      )
                                                      : Text(''),
                                              onPress: () => _playVideo(file),
                                            ),
                                  )
                                  .toList(),
                        );
                      }
                      return const Center(child: Text('加载失败'));
                    },
                  ),
                ),
      ),
    );
  }
}
