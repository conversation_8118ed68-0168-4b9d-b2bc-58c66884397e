import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:canvas_danmaku/canvas_danmaku.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:video_player/video_player.dart';
import '../model/danmaku.dart';
import '../model/player_state.dart';
import '../service/player.dart';
import '../service/history.dart';
import '../widget/video_player_gesture_detector.dart';
import '../widget/volume_control_widget.dart';
import '../widget/brightness_control_widget.dart';
import '../widget/playback_speed_panel.dart';
import '../widget/danmaku_settings_panel.dart';
import '../widget/player_notification_widget.dart';

class VideoPlayerPage extends StatefulWidget {
  final String videoPath;
  final String headers;

  const VideoPlayerPage({
    super.key,
    required this.videoPath,
    required this.headers,
  });

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late VideoPlayerService _playerService;

  // 控制栏显示状态
  bool _showControls = true;
  Timer? _hideControlsTimer;
  Timer? _historyUpdateTimer;

  // 手势控制状态
  bool _isGesturing = false; // 是否正在进行手势操作
  bool _showVolumeControl = false;
  bool _showBrightnessControl = false;
  bool _showProgressIndicator = false; // 用于显示拖动进度
  bool _showSpeedPanel = false;
  bool _showSpeedIndicator = false;
  bool _showDanmakuSettings = false;

  // 当前控制值
  double _currentVolume = 0.5;
  double _currentBrightness = 0.5;
  String _progressIndicatorText = "";

  // 手势拖动初始值
  double? _initialVolumeOnPan;
  double? _initialBrightnessOnPan;
  Duration? _initialPositionOnPan;

  // 通知状态
  PlayerNotification? _currentNotification;

  // 弹幕控制器
  DanmakuController? _danmakuController;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeDanmaku();
    _initializePlayer();
  }

  /// 初始化服务
  void _initializeServices() {
    _playerService = GetIt.I<VideoPlayerService>();

    // 初始化音量和亮度控制
    VolumeControlService.initialize();
    BrightnessControlService.initialize();

    // 初始化通知管理器
    PlayerNotificationManager.addListener(_onNotificationChanged);

    // 开始历史记录定时更新
    _startHistoryUpdateTimer();
  }

  /// 初始化弹幕控制器
  void _initializeDanmaku() {
    _danmakuController = DanmakuController(
      onAddDanmaku: (danmaku) {
        // 处理添加弹幕事件
        debugPrint('添加弹幕: ${danmaku.text}');
      },
      onUpdateOption: (option) {
        // 处理弹幕选项更新事件
        debugPrint('更新弹幕选项: $option');
      },
      onPause: () {
        // 处理弹幕暂停事件
        debugPrint('弹幕暂停');
      },
      onResume: () {
        // 处理弹幕恢复事件
        debugPrint('弹幕恢复');
      },
      onClear: () {
        // 处理弹幕清除事件
        debugPrint('弹幕清除');
      },
    );
  }

  /// 通知变化回调
  void _onNotificationChanged() {
    if (mounted) {
      setState(() {
        _currentNotification = PlayerNotificationManager.currentNotification;
      });
    }
  }

  /// 开始历史记录定时更新
  void _startHistoryUpdateTimer() {
    _historyUpdateTimer?.cancel();
    _historyUpdateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _playerService.updatePlaybackHistory();
    });
  }

  /// 初始化播放器
  Future<void> _initializePlayer() async {
    try {
      final headers = jsonDecode(widget.headers);
      await _playerService.initialize(
        videoPath: widget.videoPath,
        headers: headers.cast<String, String>(),
      );

      // 恢复播放历史
      await _restorePlaybackHistory();

      // 自动播放
      await _playerService.play();

      // 显示控制栏
      _showControlsTemporarily();

      // 显示初始化成功通知
      PlayerNotificationManager.showInfo(
        '视频加载成功',
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      debugPrint('初始化播放器失败: $e');
      // 显示错误通知
      PlayerNotificationManager.showError(
        '播放器初始化失败: $e',
        duration: const Duration(seconds: 5),
      );
    }
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _historyUpdateTimer?.cancel();
    _playerService.dispose();

    // 移除通知监听器
    PlayerNotificationManager.removeListener(_onNotificationChanged);

    // 恢复系统UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // 释放音量和亮度控制服务
    VolumeControlService.dispose();
    BrightnessControlService.dispose();

    // 释放弹幕控制器
    _danmakuController = null;

    super.dispose();
  }

  /// 显示控制栏并设置自动隐藏
  void _showControlsTemporarily() {
    setState(() {
      // 如果正在手势操作，则不自动隐藏
      if (!_isGesturing) {
        _showControls = true;
      }
    });

    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && !_isGesturing) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  /// 切换播放/暂停
  void _togglePlayPause() {
    _playerService.togglePlayPause();
    _showControlsTemporarily();
    _syncDanmakuWithVideo();
  }

  /// 恢复播放历史
  Future<void> _restorePlaybackHistory() async {
    try {
      final historyService = GetIt.I<HistoryService>();
      final history = await historyService.getPlaybackHistory(widget.videoPath);

      if (history != null && history.position > 0) {
        final position = Duration(milliseconds: history.position);
        final duration = Duration(milliseconds: history.duration);

        // 如果播放进度超过95%，从头开始播放
        if (position.inMilliseconds / duration.inMilliseconds > 0.95) {
          debugPrint('视频已播放完成，从头开始');
          return;
        }

        // 跳转到上次播放位置
        await _playerService.seekTo(position);

        // 显示恢复播放通知
        final positionText = _formatDuration(position);
        PlayerNotificationManager.showInfo(
          '已恢复到 $positionText',
          duration: const Duration(seconds: 3),
        );

        debugPrint('恢复播放历史: $positionText');
      }
    } catch (e) {
      debugPrint('恢复播放历史失败: $e');
    }
  }

  /// 格式化时长显示
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 同步弹幕与视频播放状态
  void _syncDanmakuWithVideo() {
    if (_danmakuController == null) return;

    final playerState = _playerService.playerState.value;
    // final position = _playerService.position.value;

    if (playerState.isPlaying) {
      _danmakuController!.resume();
      // 弹幕时间同步（如果API支持的话）
      // _danmakuController!.updateTime(position.inMilliseconds);
    } else if (playerState.isPaused) {
      _danmakuController!.pause();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        autofocus: true,
        onKeyEvent: _handleKeyEvent,
        child: Watch((context) => _buildBody()),
      ),
    );
  }

  /// 处理键盘事件
  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.space:
          _togglePlayPause();
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowLeft:
          _seekRelative(const Duration(seconds: -10));
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowRight:
          _seekRelative(const Duration(seconds: 10));
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowUp:
          _adjustVolume(0.1);
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowDown:
          _adjustVolume(-0.1);
          return KeyEventResult.handled;
        case LogicalKeyboardKey.escape:
          context.pop();
          return KeyEventResult.handled;
        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  /// 相对跳转
  void _seekRelative(Duration offset) {
    final currentPosition = _playerService.position.value;
    final newPosition = currentPosition + offset;
    _playerService.seekTo(newPosition);

    // 显示跳转通知
    final offsetText =
        offset.isNegative
            ? '-${_formatDuration(-offset)}'
            : '+${_formatDuration(offset)}';
    PlayerNotificationManager.showInfo(
      '跳转 $offsetText',
      duration: const Duration(seconds: 2),
    );
  }

  Widget _buildBody() {
    final playerState = _playerService.playerState.watch(context);
    final errorMessage = _playerService.errorMessage.watch(context);

    if (playerState.hasError) {
      return _buildErrorWidget(errorMessage);
    }

    if (playerState.isLoading || playerState == PlayerState.idle) {
      return _buildLoadingWidget();
    }

    return _buildVideoPlayer();
  }

  Widget _buildErrorWidget(String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          const Text(
            '视频加载失败',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage ?? '未知错误',
            style: const TextStyle(color: Colors.white70, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FButton(
            onPress: () {
              _initializePlayer();
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: Colors.white),
          const SizedBox(height: 16),
          Watch((context) {
            final isDanmakuLoading = _playerService.isDanmakuLoading.watch(
              context,
            );
            return Column(
              children: [
                const Text(
                  '正在加载视频...',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
                if (isDanmakuLoading) ...[
                  const SizedBox(height: 8),
                  const Text(
                    '正在加载弹幕...',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                ],
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return VideoPlayerGestureDetector(
      onSingleTap: () {
        if (_showControls) {
          setState(() {
            _showControls = false;
          });
        } else {
          _showControlsTemporarily();
        }
      },
      onDoubleTap: _togglePlayPause,
      onLongPressStart: () {
        setState(() {
          _showSpeedIndicator = true;
          // 手势开始，隐藏控制栏
          _isGesturing = true;
          _showControls = false;
        });
        _playerService.setPlaybackSpeed(2.0);
      },
      onLongPressEnd: () {
        setState(() {
          _showSpeedIndicator = false;
          // 手势结束，显示控制栏
          _isGesturing = false;
        });
        _playerService.setPlaybackSpeed(1.0);
        _showControlsTemporarily();
      },
      onPanStart: () {
        // 记录手势开始时的初始值
        _initialVolumeOnPan = _currentVolume;
        _initialBrightnessOnPan = _currentBrightness;
        _initialPositionOnPan = _playerService.position.value;

        setState(() {
          _isGesturing = true;
          _showControls = false;
        });
      },
      onPanEnd: () {
        setState(() {
          _isGesturing = false;
          _showVolumeControl = false;
          _showBrightnessControl = false;
          _showProgressIndicator = false;
        });
        _showControlsTemporarily();
      },
      onVerticalDragLeft: (delta) {
        _adjustBrightness(delta);
      },
      onVerticalDragRight: (delta) {
        _adjustVolume(delta);
      },
      onHorizontalDrag: (delta) {
        _adjustProgress(delta);
      },
      child: Stack(
        children: [
          // 视频播放器
          _buildVideoPlayerWidget(),
          // 弹幕层
          _buildDanmakuLayer(),
          // 控制栏
          if (_showControls && !_isGesturing) _buildControls(),
          // 音量控制
          if (_showVolumeControl) _buildVolumeControlOverlay(),
          // 亮度控制
          if (_showBrightnessControl) _buildBrightnessControlOverlay(),
          // 进度指示器
          if (_showProgressIndicator) _buildProgressIndicatorOverlay(),
          // 倍速指示器
          if (_showSpeedIndicator) _buildSpeedIndicatorOverlay(),
          // 倍速面板
          if (_showSpeedPanel) _buildSpeedPanelOverlay(),
          // 弹幕设置面板
          if (_showDanmakuSettings) _buildDanmakuSettingsOverlay(),
          // 缓冲指示器
          _buildBufferingIndicator(),
          // 通知组件
          _buildNotificationOverlay(),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // 顶部控制栏
          _buildTopControls(),
          const Spacer(),
          // 底部控制栏
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              icon: const Icon(FIcons.arrowLeft, color: Colors.white),
              onPressed: () => context.pop(),
            ),
            Row(
              children: [
                // 弹幕开关
                Watch((context) {
                  final settings = _playerService.danmakuSettings.watch(
                    context,
                  );
                  return IconButton(
                    icon: Icon(
                      settings.enabled
                          ? Icons.subtitles_outlined
                          : Icons.subtitles_off_outlined,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _playerService.updateDanmakuSettings(
                        settings.copyWith(enabled: !settings.enabled),
                      );
                    },
                  );
                }),
                const SizedBox(width: 16),
                // 弹幕设置
                IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onPressed: () {
                    _showDanmakuActionsDrawer(context);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 进度条
            _buildProgressBar(),
            const SizedBox(height: 8),
            // 播放控制按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 左侧按钮组
                Row(
                  children: [
                    // 上一个视频
                    IconButton(
                      icon: const Icon(
                        Icons.skip_previous,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: () => _playAdjacentVideo(isNext: false),
                    ),
                    const SizedBox(width: 16),
                    // 播放/暂停
                    Watch((context) {
                      final playerState = _playerService.playerState.watch(
                        context,
                      );
                      return IconButton(
                        icon: Icon(
                          playerState.isPlaying
                              ? Icons.pause
                              : Icons.play_arrow,
                          color: Colors.white,
                          size: 48,
                        ),
                        onPressed: _togglePlayPause,
                      );
                    }),
                    const SizedBox(width: 16),
                    // 下一个视频
                    IconButton(
                      icon: const Icon(
                        Icons.skip_next,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: () => _playAdjacentVideo(isNext: true),
                    ),
                  ],
                ),
                // 右侧按钮组
                Row(
                  children: [
                    // 选集
                    IconButton(
                      icon: const Icon(
                        Icons.video_library_outlined,
                        color: Colors.white,
                        size: 28,
                      ),
                      onPressed: _showEpisodeListPanel,
                    ),
                    const SizedBox(width: 16),
                    // 速度控制
                    Watch((context) {
                      final speed = _playerService.playbackSpeed.watch(context);
                      return TextButton(
                        onPressed: () {
                          _showSpeedSelectionDrawer(context);
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.white,
                          textStyle: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        child: Text('${speed}x'),
                      );
                    }),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  /// 构建视频播放器组件
  Widget _buildVideoPlayerWidget() {
    final controller = _playerService.controller;
    if (controller == null || !controller.value.isInitialized) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    return Center(
      child: AspectRatio(
        aspectRatio: controller.value.aspectRatio,
        child: VideoPlayer(controller),
      ),
    );
  }

  /// 构建弹幕层
  Widget _buildDanmakuLayer() {
    return Watch((context) {
      // final danmakus = _playerService.danmakus.watch(context);
      final settings = _playerService.danmakuSettings.watch(context);
      // final position = _playerService.position.watch(context);

      if (!settings.enabled || _danmakuController == null) {
        return Container();
      }

      return DanmakuScreen(
        createdController: (controller) {
          _danmakuController = controller;
        },
        option: DanmakuOption(
          fontSize: 16.0 * settings.fontSizeScale,
          opacity: settings.opacity,
          fontWeight: settings.fontWeight.index,
          showStroke: true,
          area: settings.density,
          duration: (8 / settings.speedScale).round(),
        ),
      );
    });
  }

  /// 构建音量控制覆盖层
  Widget _buildVolumeControlOverlay() {
    return Positioned(
      right: 50,
      top: 0,
      bottom: 0,
      child: Center(
        child: VolumeControlWidget(
          volume: _currentVolume,
          isVisible: _showVolumeControl,
        ),
      ),
    );
  }

  /// 构建亮度控制覆盖层
  Widget _buildBrightnessControlOverlay() {
    return Positioned(
      left: 50,
      top: 0,
      bottom: 0,
      child: Center(
        child: BrightnessControlWidget(
          brightness: _currentBrightness,
          isVisible: _showBrightnessControl,
        ),
      ),
    );
  }

  /// 构建倍速指示器覆盖层
  Widget _buildSpeedIndicatorOverlay() {
    return Positioned(
      top: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Watch((context) {
          final speed = _playerService.playbackSpeed.watch(context);
          return PlaybackSpeedIndicator(
            speed: speed,
            isVisible: _showSpeedIndicator,
          );
        }),
      ),
    );
  }

  /// 构建倍速面板覆盖层
  Widget _buildSpeedPanelOverlay() {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.5),
        child: Center(
          child: Watch((context) {
            final speed = _playerService.playbackSpeed.watch(context);
            return PlaybackSpeedPanel(
              currentSpeed: speed,
              onSpeedChanged: (newSpeed) {
                _playerService.setPlaybackSpeed(newSpeed);
                setState(() {
                  _showSpeedPanel = false;
                });
                // 显示速度变更通知
                PlayerNotificationManager.showInfo(
                  '播放速度: ${newSpeed}x',
                  duration: const Duration(seconds: 2),
                );
              },
              onClose: () {
                setState(() {
                  _showSpeedPanel = false;
                });
              },
            );
          }),
        ),
      ),
    );
  }

  /// 构建弹幕设置面板覆盖层 (保留此空方法以避免破坏旧逻辑，但实际不再使用)
  Widget _buildDanmakuSettingsOverlay() {
    if (_showDanmakuSettings) {
      // 仅在旧状态被意外触发时调用一次新的抽屉方法
      Future.microtask(() => _showDanmakuSettingsDrawer(context));
      // 立刻重置状态
      Future.microtask(() => setState(() => _showDanmakuSettings = false));
    }
    return const SizedBox.shrink();
  }

  /// 构建缓冲指示器
  Widget _buildBufferingIndicator() {
    return Watch((context) {
      final playerState = _playerService.playerState.watch(context);

      if (playerState != PlayerState.buffering) {
        return const SizedBox.shrink();
      }

      return const Positioned.fill(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
              SizedBox(height: 8),
              Text(
                '缓冲中...',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建通知覆盖层
  Widget _buildNotificationOverlay() {
    return Positioned(
      left: 16,
      bottom: 100, // 在底部控制栏外的位置
      child: PlayerNotificationWidget(
        notification: _currentNotification,
        onDismiss: () {
          PlayerNotificationManager.hideNotification();
        },
      ),
    );
  }

  /// 调整音量
  void _adjustVolume(double deltaY) {
    if (_initialVolumeOnPan == null) return;
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight <= 0) return;

    if (!_showVolumeControl) {
      setState(() {
        _showVolumeControl = true;
      });
    }

    final newVolume = (_initialVolumeOnPan! + deltaY / screenHeight).clamp(
      0.0,
      1.0,
    );
    setState(() {
      _currentVolume = newVolume;
      VolumeControlService.setVolume(_currentVolume);
    });
  }

  /// 调整亮度
  void _adjustBrightness(double deltaY) {
    if (_initialBrightnessOnPan == null) return;
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight <= 0) return;

    if (!_showBrightnessControl) {
      setState(() {
        _showBrightnessControl = true;
      });
    }
    final newBrightness = (_initialBrightnessOnPan! + deltaY / screenHeight)
        .clamp(0.0, 1.0);
    setState(() {
      _currentBrightness = newBrightness;
      BrightnessControlService.setBrightness(_currentBrightness);
    });
  }

  /// 调整播放进度
  void _adjustProgress(double deltaX) {
    if (_initialPositionOnPan == null) return;
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 0) return;

    final duration = _playerService.duration.value;
    if (duration.inMilliseconds <= 0) return;

    setState(() {
      _showProgressIndicator = true;
    });

    // 从最左到最右滑动，快进90秒
    final seekOffset = Duration(seconds: (deltaX / screenWidth * 90).round());
    final newPosition = (_initialPositionOnPan! + seekOffset);

    // 限制在视频时长范围内
    final clampedPosition = newPosition.inMilliseconds.clamp(
      0,
      duration.inMilliseconds,
    );
    final finalPosition = Duration(milliseconds: clampedPosition);

    _playerService.seekTo(finalPosition);

    // 更新UI显示
    final newPositionText = _formatDuration(finalPosition);
    final durationText = _formatDuration(duration);
    setState(() {
      _progressIndicatorText = '$newPositionText / $durationText';
    });
  }

  /// 构建进度指示器覆盖层
  Widget _buildProgressIndicatorOverlay() {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          _progressIndicatorText,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Watch((context) {
      final position = _playerService.position.watch(context);
      final duration = _playerService.duration.watch(context);
      final bufferedPosition = _playerService.bufferedPosition.watch(context);

      return ProgressBar(
        progress: position,
        total: duration,
        buffered: bufferedPosition,
        thumbRadius: 8,
        thumbGlowRadius: 18,
        timeLabelTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 12.0,
          fontFeatures: [FontFeature.tabularFigures()],
        ),
        onSeek: _playerService.seekTo,
      );
    });
  }

  /// 显示速度选择侧边抽屉
  void _showSpeedSelectionDrawer(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Color(0xFF222222),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Watch((context) {
            final speed = _playerService.playbackSpeed.watch(context);
            return PlaybackSpeedPanel(
              currentSpeed: speed,
              onSpeedChanged: (newSpeed) {
                _playerService.setPlaybackSpeed(newSpeed);
                Navigator.pop(context);
                PlayerNotificationManager.showInfo(
                  '播放速度: ${newSpeed}x',
                  duration: const Duration(seconds: 2),
                );
              },
              onClose: () => Navigator.pop(context),
            );
          }),
        );
      },
    );
  }

  /// 显示弹幕操作侧边抽屉
  void _showDanmakuActionsDrawer(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Color(0xFF222222),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: SafeArea(
            child: Wrap(
              children: [
                ListTile(
                  leading: const Icon(Icons.search, color: Colors.white),
                  title: const Text(
                    '手动匹配弹幕',
                    style: TextStyle(color: Colors.white),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    PlayerNotificationManager.showInfo(
                      '正在匹配弹幕...',
                      duration: const Duration(seconds: 2),
                    );
                    _playerService.forceMatchAndLoadDanmaku();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.refresh, color: Colors.white),
                  title: const Text(
                    '重新加载弹幕',
                    style: TextStyle(color: Colors.white),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    PlayerNotificationManager.showInfo(
                      '正在重新加载弹幕...',
                      duration: const Duration(seconds: 2),
                    );
                    _playerService.forceRefreshDanmakus();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_open, color: Colors.white),
                  title: const Text(
                    '替换本地弹幕',
                    style: TextStyle(color: Colors.white),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _replaceLocalDanmaku();
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.settings_outlined,
                    color: Colors.white,
                  ),
                  title: const Text(
                    '弹幕设置',
                    style: TextStyle(color: Colors.white),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showDanmakuSettingsDrawer(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 播放相邻的视频文件
  Future<void> _playAdjacentVideo({required bool isNext}) async {
    try {
      final currentPath = _playerService.currentVideoPath;
      if (currentPath == null || currentPath.startsWith('http')) {
        PlayerNotificationManager.showInfo('仅支持本地文件切换');
        return;
      }

      final currentFile = File(currentPath);
      final directory = currentFile.parent;
      final files =
          await directory
              .list()
              .where(
                (item) =>
                    item is File &&
                    (item.path.endsWith('.mp4') ||
                        item.path.endsWith('.mkv') ||
                        item.path.endsWith('.avi') ||
                        item.path.endsWith('.mov')),
              )
              .toList();

      // 按文件名排序
      files.sort((a, b) => a.path.compareTo(b.path));

      final currentIndex = files.indexWhere((f) => f.path == currentPath);
      if (currentIndex == -1) {
        PlayerNotificationManager.showError('找不到当前文件');
        return;
      }

      final nextIndex = isNext ? currentIndex + 1 : currentIndex - 1;

      if (nextIndex >= 0 && nextIndex < files.length) {
        final nextVideoPath = files[nextIndex].path;
        // 使用一个新的PlayerService实例或重新初始化当前实例
        // 这里采用重新导航到自己的方式来重建页面和播放器
        // ignore: use_build_context_synchronously
        if (!context.mounted) return;
        context.pushReplacement(
          '/player',
          extra: {'videoPath': nextVideoPath, 'headers': widget.headers},
        );
      } else {
        PlayerNotificationManager.showInfo(isNext ? '已经是最后一个视频' : '已经是第一个视频');
      }
    } catch (e) {
      debugPrint('切换视频失败: $e');
      PlayerNotificationManager.showError('切换视频失败: $e');
    }
  }

  /// 显示选集面板
  void _showEpisodeListPanel() {
    // 隐藏主控制栏以避免重叠
    setState(() {
      _showControls = false;
    });

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => _EpisodeListPanel(
            currentPath: _playerService.currentVideoPath!,
            onEpisodeSelected: (selectedPath) {
              Navigator.pop(context);
              if (_playerService.currentVideoPath != selectedPath) {
                context.pushReplacement(
                  '/player',
                  extra: {'videoPath': selectedPath, 'headers': widget.headers},
                );
              }
            },
          ),
    ).whenComplete(() {
      // 抽屉关闭后，恢复控制栏的自动隐藏逻辑
      _showControlsTemporarily();
    });
  }

  /// 显示弹幕设置侧边抽屉
  void _showDanmakuSettingsDrawer(BuildContext context) {
    // 隐藏主控制栏以避免重叠
    setState(() {
      _showControls = false;
    });
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true, // 允许内容决定高度
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5, // 初始高度
          minChildSize: 0.3, // 最小高度
          maxChildSize: 0.8, // 最大高度
          expand: false,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              decoration: const BoxDecoration(
                color: Color(0xFF222222),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Watch((context) {
                final settings = _playerService.danmakuSettings.watch(context);
                return DanmakuSettingsPanel(
                  settings: settings,
                  onSettingsChanged: (newSettings) {
                    _playerService.updateDanmakuSettings(newSettings);
                  },
                  onClose: () {
                    Navigator.of(context).pop();
                  },
                  scrollController: scrollController, // 传递滚动控制器
                );
              }),
            );
          },
        );
      },
    ).whenComplete(() {
      // 抽屉关闭后，恢复控制栏的自动隐藏逻辑
      _showControlsTemporarily();
    });
  }

  /// 替换本地弹幕文件
  Future<void> _replaceLocalDanmaku() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xml', 'json'],
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        PlayerNotificationManager.showInfo('正在加载本地弹幕...');

        // 调用服务层来处理文件加载和解析
        final success = await _playerService.loadDanmakuFromFile(filePath);
        if (success) {
          PlayerNotificationManager.showSuccess('本地弹幕加载成功');
          // 弹幕会自动更新，因为UI监听了danmakus信号
        } else {
          PlayerNotificationManager.showError('弹幕文件格式无效');
        }
      } else {
        // User canceled the picker
      }
    } catch (e) {
      debugPrint('选择或加载本地弹幕失败: $e');
      PlayerNotificationManager.showError('加载本地弹幕失败: $e');
    }
  }
}

/// 选集面板
class _EpisodeListPanel extends StatefulWidget {
  final String currentPath;
  final ValueChanged<String> onEpisodeSelected;

  const _EpisodeListPanel({
    required this.currentPath,
    required this.onEpisodeSelected,
  });

  @override
  State<_EpisodeListPanel> createState() => _EpisodeListPanelState();
}

class _EpisodeListPanelState extends State<_EpisodeListPanel> {
  late Future<List<File>> _episodeListFuture;

  @override
  void initState() {
    super.initState();
    _episodeListFuture = _loadEpisodes();
  }

  Future<List<File>> _loadEpisodes() async {
    try {
      if (widget.currentPath.startsWith('http')) {
        return []; // 网络文件不支持选集
      }
      final currentFile = File(widget.currentPath);
      final directory = currentFile.parent;
      final files =
          await directory
              .list()
              .where(
                (item) =>
                    item is File &&
                    (item.path.endsWith('.mp4') ||
                        item.path.endsWith('.mkv') ||
                        item.path.endsWith('.avi') ||
                        item.path.endsWith('.mov')),
              )
              .cast<File>()
              .toList();

      files.sort((a, b) => a.path.compareTo(b.path));
      return files;
    } catch (e) {
      debugPrint('加载剧集列表失败: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Color(0xFF222222),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              '选集',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: Colors.white),
            ),
          ),
          const Divider(color: Colors.white24, height: 1),
          Expanded(
            child: FutureBuilder<List<File>>(
              future: _episodeListFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError ||
                    !snapshot.hasData ||
                    snapshot.data!.isEmpty) {
                  return const Center(
                    child: Text(
                      '没有找到其他视频文件',
                      style: TextStyle(color: Colors.white70),
                    ),
                  );
                }

                final episodes = snapshot.data!;
                return ListView.builder(
                  itemCount: episodes.length,
                  itemBuilder: (context, index) {
                    final episodeFile = episodes[index];
                    final isSelected = episodeFile.path == widget.currentPath;
                    return ListTile(
                      title: Text(
                        episodeFile.path.split(Platform.pathSeparator).last,
                        style: TextStyle(
                          color:
                              isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.white,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      onTap: () => widget.onEpisodeSelected(episodeFile.path),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
