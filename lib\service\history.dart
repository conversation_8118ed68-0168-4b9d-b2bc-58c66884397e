import 'dart:async';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:get_it/get_it.dart';
import 'package:signals/signals.dart';
import 'package:drift/drift.dart';

import '../utils/crypto_utils.dart';

class HistoryService {
  final StorageService storage;

  // --- Start: Merged from PlaybackHistoryService ---

  // 当前正在记录的视频信息
  String? _currentVideoPath;

  // 播放进度更新信号
  final Signal<Duration> _currentPosition = Signal(Duration.zero);
  final Signal<Duration> _currentDuration = Signal(Duration.zero);

  /// 当前播放位置
  ReadonlySignal<Duration> get currentPosition => _currentPosition.readonly();

  /// 当前视频总长度
  ReadonlySignal<Duration> get currentDuration => _currentDuration.readonly();

  // --- End: Merged from PlaybackHistoryService ---

  HistoryService({required this.storage});

  static Future<void> register() async {
    final service = HistoryService(storage: GetIt.I.get<StorageService>());
    GetIt.I.registerSingleton<HistoryService>(service);
  }

  /// Get history by unique key (MD5 of video path)
  Future<History?> getHistoryByUniqueKey(String uniqueKey) async {
    return await (storage.select(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).getSingleOrNull();
  }

  /// Get all histories
  Future<List<History>> getAllHistories() async {
    return await storage.select(storage.histories).get();
  }

  /// Update or insert history
  Future<void> updateHistory(History history) async {
    await storage
        .into(storage.histories)
        .insertOnConflictUpdate(history.toCompanion(false));
  }

  /// Delete history by unique key
  Future<void> deleteHistoryByUniqueKey(String uniqueKey) async {
    await (storage.delete(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).go();
  }

  /// Delete history by video path
  Future<void> deleteHistoryByVideoPath(String videoPath) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    await deleteHistoryByUniqueKey(uniqueKey);
  }

  /// Get histories by URL pattern (for filtering by media library)
  Future<List<History>> getHistoriesByUrlPattern(String urlPattern) async {
    // TODO: Implement the actual filtering logic
    return await storage.select(storage.histories).get();
  }

  /// Clear all histories
  Future<void> clearAllHistories() async {
    await storage.delete(storage.histories).go();
  }

  // --- Start: Merged from PlaybackHistoryService ---

  /// 开始记录播放历史
  Future<void> startRecording({
    required String videoPath,
    required String url,
    required String headers,
    String? snapshot,
  }) async {
    // 停止之前的记录
    stopRecording();

    _currentVideoPath = videoPath;

    // 生成唯一键
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);

    // 检查是否已存在历史记录
    final existingHistory = await getHistoryByUniqueKey(uniqueKey);

    if (existingHistory == null) {
      // 创建新的历史记录
      final history = History(
        id: 0, // 自动生成
        uniqueKey: uniqueKey,
        duration: 0, // 初始为0，后续更新
        position: 0,
        url: url,
        headers: headers,
        updateTime: DateTime.now().millisecondsSinceEpoch,
        snapshot: snapshot ?? '',
        danmakuPath: '',
        danmakuUpdateTime: 0,
      );

      await updateHistory(history);
    }
  }

  /// 停止记录播放历史
  void stopRecording() {
    _currentVideoPath = null;
  }

  /// 更新播放进度
  Future<void> updateProgress({
    required Duration position,
    required Duration duration,
  }) async {
    if (_currentVideoPath == null) return;

    // 更新信号
    _currentPosition.value = position;
    _currentDuration.value = duration;

    final uniqueKey = CryptoUtils.generateVideoUniqueKey(_currentVideoPath!);

    // 使用 partial update 来提高性能
    final companion = HistoriesCompanion(
      position: Value(position.inMilliseconds),
      duration: Value(duration.inMilliseconds),
      updateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );

    await (storage.update(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).write(companion);
  }

  /// 保存快照
  Future<void> saveSnapshot({
    required String videoPath,
    required String snapshot,
  }) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    final companion = HistoriesCompanion(
      snapshot: Value(snapshot),
      updateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );
    await (storage.update(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).write(companion);
  }

  /// 保存弹幕路径
  Future<void> saveDanmakuPath({
    required String videoPath,
    required String danmakuPath,
  }) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    final companion = HistoriesCompanion(
      danmakuPath: Value(danmakuPath),
      danmakuUpdateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );
    await (storage.update(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).write(companion);
  }

  /// 获取播放历史
  Future<History?> getPlaybackHistory(String videoPath) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    return await getHistoryByUniqueKey(uniqueKey);
  }

  /// 获取播放进度百分比
  double getProgressPercentage(String videoPath) {
    // 这里可以从缓存中获取，避免频繁数据库查询
    // 简化实现，实际使用时可以优化
    return 0.0;
  }

  /// 判断视频是否已观看完成
  bool isVideoCompleted(String videoPath) {
    // 简化实现，可以根据播放进度判断
    return false;
  }

  /// 释放资源
  void dispose() {
    stopRecording();
    _currentPosition.dispose();
    _currentDuration.dispose();
  }

  // --- End: Merged from PlaybackHistoryService ---
}
