import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:fvp/fvp.dart' as fvp;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // register services
  await ServiceLocator.initialize();
  await MediaLibraryService.register();
  await HistoryService.register();
  await ConfigureService.register();

  fvp.registerWith();

  runApp(Application());
}

class Application extends StatelessWidget {
  Application({super.key});
  final fThemeData = FThemes.blue.light;

  @override
  Widget build(BuildContext context) => MaterialApp.router(
    theme: fThemeData.toApproximateMaterialTheme(),
    builder:
        (context, child) =>
            FToaster(child: FTheme(data: fThemeData, child: child!)),
    routerConfig: router,
  );
}
