import 'package:get_it/get_it.dart';
import 'storage.dart';
import 'history.dart';
import 'player.dart';

/// 服务定位器配置
/// 管理所有服务的依赖注入
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;

  /// 初始化所有服务
  static Future<void> initialize() async {
    // 注册存储服务
    await StorageService.register();

    // 注册历史记录服务
    await HistoryService.register();

    // 注册视频播放服务
    _getIt.registerFactory<VideoPlayerService>(() => VideoPlayerService());
  }

  /// 获取服务实例
  static T get<T extends Object>() => _getIt.get<T>();

  /// 重置所有服务（用于测试）
  static Future<void> reset() async {
    await _getIt.reset();
  }
}
